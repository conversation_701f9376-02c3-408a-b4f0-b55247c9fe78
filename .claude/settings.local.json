{"permissions": {"allow": ["Bash(git add:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(docker:*)", "Bash(find:*)", "<PERSON><PERSON>(true)", "Bash(rm:*)", "Bash(npm install)", "<PERSON><PERSON>(source:*)", "Bash(pip install:*)", "Bash(npm run test:*)", "Bash(python -m pytest tests/ -v)", "<PERSON><PERSON>(touch:*)", "Bash(python -m pytest tests/test_main.py -v)", "Bash(npx storybook@latest init:*)", "<PERSON><PERSON>(pkill:*)", "Bash(npm install:*)", "Bash(ls:*)", "Bash(mmdc:*)", "Bash(git commit:*)", "Bash(gh pr view:*)", "Bash(gh run view:*)", "Bash(npm run lint)", "<PERSON><PERSON>(python:*)", "Bash(git push:*)", "Bash(gh pr ready:*)", "<PERSON><PERSON>(gh issue view:*)", "mcp__ide__getDiagnostics", "<PERSON><PERSON>(gh pr edit:*)", "<PERSON><PERSON>(gh run list:*)", "Bash(npm test:*)", "Bash(PYTHONPATH=. pytest tests/ -v)", "<PERSON><PERSON>(mv:*)", "Bash(PYTHONPATH=. pytest tests/ -v --tb=short)", "Bash(ruff check:*)", "Bash(gh pr checks:*)", "Bash(gh api:*)", "Bash(PYTHONPATH=. pytest --cov=src tests/ --cov-report=xml -v)", "Bash(PYTHONPATH=. pytest --cov=src tests/ --cov-report=xml)", "Bash(gh workflow run:*)", "Bash(gh pr:*)", "Bash(git fetch:*)", "Bash(git merge-base:*)", "Bash(git merge:*)", "Bash(npm run lint:*)", "<PERSON><PERSON>(sed:*)", "Bash(git checkout:*)", "Bash(grep:*)", "<PERSON><PERSON>(echo:*)", "Bash(for:*)", "Bash(do)", "Bash(done)", "WebFetch(domain:github.com)", "Bash(PYTHONPATH=. OPENAI_API_KEY=\"test-key\" ANTHROPIC_API_KEY=\"test-key\" pytest --cov=src tests/ --cov-report=xml -v)", "Bash(PYTHONPATH=. OPENAI_API_KEY=\"test-key\" ANTHROPIC_API_KEY=\"test-key\" pytest tests/integration/test_full_conversion_workflow.py::TestFullConversionWorkflow::test_qa_validation_workflow -v)", "Bash(PYTHONPATH=. OPENAI_API_KEY=\"test-key\" ANTHROPIC_API_KEY=\"test-key\" pytest --cov=src tests/ --cov-report=xml -v --tb=short)", "Bash(PYTHONPATH=. OPENAI_API_KEY=\"test-key\" ANTHROPIC_API_KEY=\"test-key\" pytest --cov=src tests/ --cov-report=xml --tb=short)", "Bash(PYTHONPATH=. OPENAI_API_KEY=\"test-key\" ANTHROPIC_API_KEY=\"test-key\" pytest --cov=src tests/ --cov-report=xml --tb=short -q)", "Bash(git reset:*)", "Bash(git rm:*)", "Bash(awk:*)", "Bash(alembic upgrade:*)", "Bash(DATABASE_URL=postgresql+asyncpg://postgres:password@localhost:5432/modporter PYTHONPATH=. alembic upgrade head)", "Bash(PYTHONPATH=. python -m pytest tests/unit/test_db_models.py::test_model_tablenames -v)", "Bash(DATABASE_URL=postgresql://postgres:password@localhost:5432/modporter PYTHONPATH=. alembic check)", "Bash(PYTHONPATH=. pytest tests/unit/test_main_unit.py::TestHealthEndpoint::test_health_check_returns_200 -v)", "Bash(PYTHONPATH=. pytest tests/unit/ -x --tb=short)", "Bash(PYTHONPATH=. pytest tests/unit/test_main_unit.py::TestConversionEndpoints::test_start_conversion -v)", "Bash(PYTHONPATH=. pytest tests/unit/test_main_unit.py::TestConversionEndpoints::test_start_conversion -v --tb=short)", "Bash(PYTHONPATH=. pytest tests/unit/test_main_unit.py::TestConversionEndpoints::test_cancel_conversion -v)", "Bash(PYTHONPATH=. pytest tests/unit/ --tb=short)", "Bash(PYTHONPATH=. pytest tests/unit/test_main_unit.py -k \"not test_download_converted_mod_not_found\" --tb=short)", "Bash(PYTHONPATH=. pytest tests/unit/test_main_unit.py::TestHealthEndpoint -v)", "Bash(PYTHONPATH=. pytest tests/integration/test_api_integration.py::TestConversionIntegration::test_start_conversion_workflow -v --tb=short)", "Bash(PYTHONPATH=. pytest tests/integration/test_api_integration.py::TestConversionIntegration::test_check_conversion_status -v --tb=short)", "Bash(PYTHONPATH=. pytest tests/integration/test_api_integration.py::TestErrorHandlingIntegration::test_convert_nonexistent_file -v --tb=short)", "Bash(PYTHONPATH=. pytest tests/integration/test_api_integration.py::TestErrorHandlingIntegration::test_check_status_nonexistent_job -v --tb=short)", "Bash(PYTHONPATH=. pytest tests/integration/test_api_integration.py::TestFullWorkflowIntegration::test_complete_conversion_workflow -v --tb=short)", "Bash(PYTHONPATH=. pytest tests/unit/test_main_unit.py::TestConversionEndpoints::test_download_converted_mod_not_found -v --tb=short)", "Bash(PYTHONPATH=. pytest tests/integration/test_api_integration.py::TestConversionIntegration::test_start_conversion_workflow tests/integration/test_api_integration.py::TestConversionIntegration::test_check_conversion_status tests/integration/test_api_integration.py::TestErrorHandlingIntegration::test_convert_nonexistent_file tests/integration/test_api_integration.py::TestErrorHandlingIntegration::test_check_status_nonexistent_job tests/integration/test_api_integration.py::TestFullWorkflowIntegration::test_complete_conversion_workflow tests/unit/test_main_unit.py::TestConversionEndpoints::test_download_converted_mod_not_found -v)", "<PERSON><PERSON>(curl:*)", "Bash(fuser:*)", "Bash(npm run dev:backend:*)", "Bash(ss:*)", "Bash(kill:*)", "Bash(rg:*)", "Bash(PYTHONPATH=. python -c \"from src.agents.asset_converter import AssetConverterAgent; print(''asset_converter import successful'')\")", "Bash(PYTHONPATH=. python -c \"from src.agents.bedrock_architect import BedrockArchitectAgent; print(''bedrock_architect import successful'')\")", "Bash(PYTHONPATH=. python -c \"from src.agents.java_analyzer import JavaAnalyzerAgent; from src.agents.logic_translator import LogicTranslatorAgent; from src.agents.packaging_agent import PackagingAgent; from src.agents.qa_validator import QAValidatorAgent; print(''All remaining agents import successful'')\")", "Bash(PYTHONPATH=. python:*)"], "deny": []}}